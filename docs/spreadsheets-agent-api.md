# SpreadsheetsAgent API Documentation

The SpreadsheetsAgent provides an intelligent, agentic workflow for Google Sheets operations using LangGraph. It allows users to interact with Google Sheets through natural language requests and provides structured API endpoints for programmatic access.

## Overview

The SpreadsheetsAgent is built using:
- **LangGraph**: For creating the agentic workflow
- **<PERSON><PERSON><PERSON><PERSON>**: For structured tool calling
- **DriveUtilityApp**: For underlying Google Sheets operations
- **Credit System**: Integrated with the application's credit system

## Features

- 🤖 **Natural Language Processing**: Process spreadsheet requests in natural language
- 🔧 **5 Core Tools**: Complete CRUD operations for Google Sheets
- 💳 **Credit Integration**: Automatic credit checking and deduction
- 🛡️ **Error Handling**: Comprehensive error handling and validation
- 📊 **State Management**: Intelligent workflow state management
- 🔄 **Tool Chaining**: Ability to chain multiple operations

## API Endpoints

### Base URL
```
/api/spreadsheets-agent
```

### Authentication
All endpoints require authentication via JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

---

## Endpoints

### 1. Process Natural Language Request
Process spreadsheet operations using natural language.

**Endpoint:** `POST /api/spreadsheets-agent/process`

**Request Body:**
```json
{
  "message": "Create a new spreadsheet called 'Project Tasks' with two sheets: Tasks and Resources",
  "context": {
    "projectId": "optional-project-id"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ create_spreadsheet: Success\n   Created spreadsheet: Project Tasks",
  "data": [
    {
      "tool_call_id": "call_123",
      "name": "create_spreadsheet",
      "result": {
        "success": true,
        "spreadsheet": {
          "spreadsheetId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
          "title": "Project Tasks",
          "spreadsheetUrl": "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit"
        }
      }
    }
  ]
}
```

---

### 2. Get All Spreadsheets
Retrieve all Google Sheets spreadsheets accessible to the user.

**Endpoint:** `GET /api/spreadsheets-agent/spreadsheets`

**Query Parameters:**
- `pageSize` (optional): Number of spreadsheets to return (default: 100)
- `orderBy` (optional): Sort order (default: 'modifiedTime desc')

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ get_all_spreadsheets: Success",
  "data": [
    {
      "tool_call_id": "call_456",
      "name": "get_all_spreadsheets",
      "result": {
        "success": true,
        "spreadsheets": [
          {
            "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "name": "Project Tasks",
            "createdTime": "2024-01-15T10:30:00.000Z",
            "modifiedTime": "2024-01-15T14:20:00.000Z",
            "webViewLink": "https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit"
          }
        ],
        "totalCount": 1
      }
    }
  ]
}
```

---

### 3. Get Spreadsheet Details
Get detailed information about a specific spreadsheet including all sheets.

**Endpoint:** `GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId`

**Parameters:**
- `spreadsheetId`: The ID of the spreadsheet

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ get_spreadsheet_details: Success",
  "data": [
    {
      "tool_call_id": "call_789",
      "name": "get_spreadsheet_details",
      "result": {
        "success": true,
        "spreadsheetDetails": {
          "spreadsheetId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
          "title": "Project Tasks",
          "sheets": [
            {
              "sheetId": 0,
              "title": "Tasks",
              "index": 0,
              "gridProperties": {
                "rowCount": 1000,
                "columnCount": 26
              }
            },
            {
              "sheetId": 1,
              "title": "Resources",
              "index": 1,
              "gridProperties": {
                "rowCount": 1000,
                "columnCount": 26
              }
            }
          ]
        }
      }
    }
  ]
}
```

---

### 4. Read Spreadsheet Content
Read content from a spreadsheet.

**Endpoint:** `GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content`

**Parameters:**
- `spreadsheetId`: The ID of the spreadsheet

**Query Parameters:**
- `sheetName` (optional): Specific sheet name to read
- `range` (optional): Specific range to read (e.g., A1:C10)

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ read_spreadsheet_content: Success\n   Retrieved data from spreadsheet",
  "data": [
    {
      "tool_call_id": "call_101",
      "name": "read_spreadsheet_content",
      "result": {
        "success": true,
        "content": {
          "spreadsheetId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
          "sheetData": [
            {
              "sheetName": "Tasks",
              "values": [
                ["Task", "Status", "Assignee"],
                ["Setup project", "Complete", "John"],
                ["Design UI", "In Progress", "Jane"]
              ]
            }
          ]
        }
      }
    }
  ]
}
```

---

### 5. Update Spreadsheet Content
Update content in a spreadsheet.

**Endpoint:** `PUT /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content`

**Parameters:**
- `spreadsheetId`: The ID of the spreadsheet

**Request Body:**
```json
{
  "sheetName": "Tasks",
  "range": "A1:C3",
  "values": [
    ["Task", "Status", "Assignee"],
    ["Setup project", "Complete", "John"],
    ["Design UI", "Complete", "Jane"]
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ update_spreadsheet_content: Success\n   Updated spreadsheet successfully",
  "data": [
    {
      "tool_call_id": "call_202",
      "name": "update_spreadsheet_content",
      "result": {
        "success": true,
        "updateResult": {
          "spreadsheetId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
          "updatedRange": "Tasks!A1:C3",
          "updatedRows": 3,
          "updatedColumns": 3,
          "updatedCells": 9
        }
      }
    }
  ]
}
```

---

### 6. Create New Spreadsheet
Create a new Google Sheets spreadsheet.

**Endpoint:** `POST /api/spreadsheets-agent/spreadsheets`

**Request Body:**
```json
{
  "title": "New Project Spreadsheet",
  "sheets": [
    {
      "title": "Tasks",
      "rowCount": 500,
      "columnCount": 10
    },
    {
      "title": "Resources",
      "rowCount": 200,
      "columnCount": 8
    }
  ],
  "locale": "en_US",
  "timeZone": "America/New_York"
}
```

**Response:**
```json
{
  "success": true,
  "message": "I have completed your spreadsheet operations:\n\n✅ create_spreadsheet: Success\n   Created spreadsheet: New Project Spreadsheet",
  "data": [
    {
      "tool_call_id": "call_303",
      "name": "create_spreadsheet",
      "result": {
        "success": true,
        "spreadsheet": {
          "spreadsheetId": "1NewSpreadsheetId123456789",
          "title": "New Project Spreadsheet",
          "spreadsheetUrl": "https://docs.google.com/spreadsheets/d/1NewSpreadsheetId123456789/edit"
        }
      }
    }
  ]
}
```

---

### 7. Get Agent Status
Get the current status and health of the spreadsheet agent.

**Endpoint:** `GET /api/spreadsheets-agent/status`

**Response:**
```json
{
  "status": "healthy",
  "tools": [
    {
      "name": "get_all_spreadsheets",
      "description": "Get all Google Sheets spreadsheets accessible to the user"
    },
    {
      "name": "get_spreadsheet_details",
      "description": "Get detailed information about a specific spreadsheet including all sheets"
    },
    {
      "name": "read_spreadsheet_content",
      "description": "Read content from a Google Sheets spreadsheet"
    },
    {
      "name": "update_spreadsheet_content",
      "description": "Update content in a Google Sheets spreadsheet"
    },
    {
      "name": "create_spreadsheet",
      "description": "Create a new Google Sheets spreadsheet"
    }
  ],
  "graphInitialized": true,
  "timestamp": "2024-01-15T15:30:00.000Z"
}
```

---

### 8. Get Available Operations
Get a list of all available operations and their parameters.

**Endpoint:** `GET /api/spreadsheets-agent/operations`

**Response:**
```json
{
  "success": true,
  "operations": [
    {
      "name": "getAllSpreadsheets",
      "description": "Get all spreadsheets accessible to the user",
      "parameters": ["userId", "options?"]
    },
    {
      "name": "getSpreadsheetDetails",
      "description": "Get detailed information about a specific spreadsheet",
      "parameters": ["userId", "spreadsheetId"]
    },
    {
      "name": "readSpreadsheetContent",
      "description": "Read content from a spreadsheet",
      "parameters": ["userId", "spreadsheetId", "sheetName?", "range?"]
    },
    {
      "name": "updateSpreadsheetContent",
      "description": "Update content in a spreadsheet",
      "parameters": ["userId", "spreadsheetId", "sheetName", "range", "values"]
    },
    {
      "name": "createSpreadsheet",
      "description": "Create a new spreadsheet",
      "parameters": ["userId", "title", "options?"]
    },
    {
      "name": "processNaturalLanguageRequest",
      "description": "Process natural language spreadsheet requests",
      "parameters": ["userId", "naturalLanguageRequest"]
    }
  ]
}
```

## Error Responses

All endpoints may return the following error types:

### Authentication Error
```json
{
  "success": false,
  "error": "AUTHENTICATION_ERROR",
  "message": "User is not authenticated with Google. Please authenticate first."
}
```

### Permission Error
```json
{
  "success": false,
  "error": "PERMISSION_ERROR",
  "message": "Insufficient permissions. Please grant Google Sheets access."
}
```

### Validation Error
```json
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid input parameters.",
  "details": "Message is required and must be a non-empty string"
}
```

### Insufficient Credits
```json
{
  "success": false,
  "error": "INSUFFICIENT_CREDITS",
  "message": "Insufficient credits to process this request. Please upgrade your plan or purchase credits.",
  "creditsRequired": 1
}
```

### API Error
```json
{
  "success": false,
  "error": "API_ERROR",
  "message": "Google Sheets API error. Please try again later."
}
```

## Credit System

Each spreadsheet agent request costs **1 credit**. Credits are automatically checked before processing and deducted upon successful completion.

## Rate Limiting

API endpoints are subject to rate limiting. The default limits are:
- 100 requests per minute per user
- 1000 requests per hour per user

## Prerequisites

Before using the SpreadsheetsAgent, ensure:

1. **Google OAuth Authentication**: User must be authenticated with Google
2. **Google Sheets Permissions**: User must have granted Google Sheets access (`https://www.googleapis.com/auth/spreadsheets`)
3. **Google Drive Permissions**: User must have granted Google Drive access (`https://www.googleapis.com/auth/drive.readonly`) for listing spreadsheets
4. **Sufficient Credits**: User must have enough credits in their account

## Natural Language Examples

The agent can understand various natural language requests:

### Creating Spreadsheets
- "Create a new spreadsheet called 'Budget 2024'"
- "Make a spreadsheet for project tracking with sheets for tasks and resources"
- "Create a budget spreadsheet with monthly and yearly sheets"

### Reading Data
- "Show me all my spreadsheets"
- "Get the data from the 'Sales' sheet in my revenue spreadsheet"
- "Read cells A1 to C10 from the Tasks sheet"

### Updating Data
- "Update cell A1 in the Tasks sheet to 'Project Name'"
- "Add a new row with task data to my project spreadsheet"
- "Update the status column in my task tracker"

### Complex Operations
- "Create a project tracker, add initial task data, and share the link"
- "Find my budget spreadsheet and update this month's expenses"
- "Create a team roster spreadsheet and populate it with member information"
