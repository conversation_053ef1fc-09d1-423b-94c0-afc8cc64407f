/**
 * SpreadsheetsAgent Usage Examples
 * This file demonstrates how to use the SpreadsheetsAgent in various scenarios
 */

import { SpreadsheetsAgentService } from '../src/services/SpreadsheetsAgentService.js';

// Initialize the service
const spreadsheetsAgent = new SpreadsheetsAgentService();

/**
 * Example 1: Create a new project spreadsheet
 */
async function createProjectSpreadsheet(userId) {
  console.log('📊 Creating a new project spreadsheet...');
  
  try {
    const result = await spreadsheetsAgent.createSpreadsheet(
      userId,
      'Project Management Dashboard',
      {
        sheets: [
          { title: 'Tasks', rowCount: 1000, columnCount: 10 },
          { title: 'Resources', rowCount: 500, columnCount: 8 },
          { title: 'Timeline', rowCount: 200, columnCount: 6 }
        ],
        locale: 'en_US',
        timeZone: 'America/New_York'
      }
    );

    if (result.success) {
      console.log('✅ Spreadsheet created successfully!');
      console.log(`   Spreadsheet ID: ${result.data[0].result.spreadsheet.spreadsheetId}`);
      console.log(`   URL: ${result.data[0].result.spreadsheet.spreadsheetUrl}`);
      return result.data[0].result.spreadsheet.spreadsheetId;
    } else {
      console.error('❌ Failed to create spreadsheet:', result.message);
    }
  } catch (error) {
    console.error('❌ Error creating spreadsheet:', error.message);
  }
}

/**
 * Example 2: Populate spreadsheet with initial data
 */
async function populateSpreadsheetWithData(userId, spreadsheetId) {
  console.log('📝 Populating spreadsheet with initial data...');
  
  try {
    // Add headers and initial tasks
    const taskData = [
      ['Task ID', 'Task Name', 'Status', 'Assignee', 'Due Date', 'Priority'],
      ['T001', 'Project Setup', 'Complete', 'John Doe', '2024-01-15', 'High'],
      ['T002', 'Requirements Gathering', 'In Progress', 'Jane Smith', '2024-01-20', 'High'],
      ['T003', 'UI Design', 'Not Started', 'Bob Johnson', '2024-01-25', 'Medium'],
      ['T004', 'Backend Development', 'Not Started', 'Alice Brown', '2024-02-01', 'High']
    ];

    const result = await spreadsheetsAgent.updateSpreadsheetContent(
      userId,
      spreadsheetId,
      'Tasks',
      'A1:F5',
      taskData
    );

    if (result.success) {
      console.log('✅ Task data added successfully!');
      console.log(`   Updated ${result.data[0].result.updateResult.updatedCells} cells`);
    } else {
      console.error('❌ Failed to add task data:', result.message);
    }
  } catch (error) {
    console.error('❌ Error adding task data:', error.message);
  }
}

/**
 * Example 3: Read and analyze spreadsheet data
 */
async function analyzeSpreadsheetData(userId, spreadsheetId) {
  console.log('📊 Analyzing spreadsheet data...');
  
  try {
    const result = await spreadsheetsAgent.readSpreadsheetContent(
      userId,
      spreadsheetId,
      'Tasks',
      'A1:F10'
    );

    if (result.success) {
      const data = result.data[0].result.content.sheetData[0].values;
      console.log('✅ Data retrieved successfully!');
      
      // Analyze the data
      const headers = data[0];
      const tasks = data.slice(1);
      
      console.log(`   Total tasks: ${tasks.length}`);
      
      // Count tasks by status
      const statusCount = {};
      tasks.forEach(task => {
        const status = task[2]; // Status column
        statusCount[status] = (statusCount[status] || 0) + 1;
      });
      
      console.log('   Task status breakdown:');
      Object.entries(statusCount).forEach(([status, count]) => {
        console.log(`     - ${status}: ${count} tasks`);
      });
      
    } else {
      console.error('❌ Failed to read data:', result.message);
    }
  } catch (error) {
    console.error('❌ Error reading data:', error.message);
  }
}

/**
 * Example 4: Natural language spreadsheet operations
 */
async function naturalLanguageOperations(userId) {
  console.log('🧠 Using natural language for spreadsheet operations...');
  
  const requests = [
    "Show me all my spreadsheets",
    "Create a budget spreadsheet with monthly and yearly sheets",
    "Find my project spreadsheet and show me the task data",
    "Update the status of task T002 to 'Complete' in my project spreadsheet"
  ];

  for (const request of requests) {
    try {
      console.log(`\n🗣️  Request: "${request}"`);
      
      const result = await spreadsheetsAgent.processNaturalLanguageRequest(userId, request);
      
      if (result.success) {
        console.log('✅ Response:', result.message);
      } else {
        console.error('❌ Failed:', result.message);
      }
    } catch (error) {
      console.error('❌ Error:', error.message);
    }
  }
}

/**
 * Example 5: Batch operations
 */
async function batchOperations(userId) {
  console.log('🔄 Performing batch operations...');
  
  try {
    // Get all spreadsheets first
    const allSpreadsheetsResult = await spreadsheetsAgent.getAllSpreadsheets(userId);
    
    if (allSpreadsheetsResult.success) {
      const spreadsheets = allSpreadsheetsResult.data[0].result.spreadsheets;
      console.log(`✅ Found ${spreadsheets.length} spreadsheets`);
      
      // Get details for each spreadsheet
      for (const spreadsheet of spreadsheets.slice(0, 3)) { // Limit to first 3
        console.log(`\n📋 Getting details for: ${spreadsheet.name}`);
        
        const detailsResult = await spreadsheetsAgent.getSpreadsheetDetails(
          userId, 
          spreadsheet.id
        );
        
        if (detailsResult.success) {
          const details = detailsResult.data[0].result.spreadsheetDetails;
          console.log(`   - Sheets: ${details.sheets.length}`);
          details.sheets.forEach(sheet => {
            console.log(`     • ${sheet.title} (${sheet.gridProperties.rowCount}x${sheet.gridProperties.columnCount})`);
          });
        }
      }
    }
  } catch (error) {
    console.error('❌ Error in batch operations:', error.message);
  }
}

/**
 * Example 6: Error handling demonstration
 */
async function demonstrateErrorHandling(userId) {
  console.log('🛡️  Demonstrating error handling...');
  
  // Test various error scenarios
  const errorTests = [
    {
      name: 'Invalid spreadsheet ID',
      operation: () => spreadsheetsAgent.getSpreadsheetDetails(userId, 'invalid-id')
    },
    {
      name: 'Empty message',
      operation: () => spreadsheetsAgent.processNaturalLanguageRequest(userId, '')
    },
    {
      name: 'Invalid range format',
      operation: () => spreadsheetsAgent.readSpreadsheetContent(userId, 'valid-id', 'Sheet1', 'invalid-range')
    }
  ];

  for (const test of errorTests) {
    try {
      console.log(`\n🧪 Testing: ${test.name}`);
      const result = await test.operation();
      
      if (result.success === false) {
        console.log(`✅ Error handled correctly: ${result.error}`);
        console.log(`   Message: ${result.message}`);
      } else {
        console.log('⚠️  Expected error but operation succeeded');
      }
    } catch (error) {
      console.log(`✅ Exception caught: ${error.message}`);
    }
  }
}

/**
 * Main example runner
 */
async function runExamples() {
  const userId = 'example-user-123'; // Replace with actual user ID
  
  console.log('🚀 SpreadsheetsAgent Usage Examples\n');
  console.log('=====================================\n');

  try {
    // Example 1: Create spreadsheet
    const spreadsheetId = await createProjectSpreadsheet(userId);
    
    if (spreadsheetId) {
      // Example 2: Populate with data
      await populateSpreadsheetWithData(userId, spreadsheetId);
      
      // Example 3: Analyze data
      await analyzeSpreadsheetData(userId, spreadsheetId);
    }
    
    // Example 4: Natural language operations
    await naturalLanguageOperations(userId);
    
    // Example 5: Batch operations
    await batchOperations(userId);
    
    // Example 6: Error handling
    await demonstrateErrorHandling(userId);
    
    console.log('\n🎉 All examples completed!');
    
  } catch (error) {
    console.error('\n💥 Example execution failed:', error.message);
  }
}

/**
 * Utility function to check agent status
 */
function checkAgentStatus() {
  console.log('📊 Checking SpreadsheetsAgent status...');
  
  const status = spreadsheetsAgent.getStatus();
  console.log(`Status: ${status.status}`);
  console.log(`Tools available: ${status.tools.length}`);
  console.log(`Graph initialized: ${status.graphInitialized}`);
  
  console.log('\nAvailable operations:');
  const operations = spreadsheetsAgent.getAvailableOperations();
  operations.forEach(op => {
    console.log(`  - ${op.name}: ${op.description}`);
  });
}

// Export functions for use in other modules
export {
  createProjectSpreadsheet,
  populateSpreadsheetWithData,
  analyzeSpreadsheetData,
  naturalLanguageOperations,
  batchOperations,
  demonstrateErrorHandling,
  runExamples,
  checkAgentStatus
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  checkAgentStatus();
  console.log('\n');
  runExamples();
}
