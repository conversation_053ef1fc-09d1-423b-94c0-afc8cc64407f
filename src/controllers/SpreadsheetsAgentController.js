/**
 * @fileoverview SpreadsheetsAgentController - API endpoints for spreadsheet agent operations
 */

import { spreadsheetsAgentService } from '../services/SpreadsheetsAgentService.js';
import logger from '../config/logger.js';

/**
 * SpreadsheetsAgentController Class
 * Handles HTTP requests for spreadsheet agent operations
 */
export class SpreadsheetsAgentController {
  /**
   * Process natural language spreadsheet request
   * POST /api/spreadsheets-agent/process
   */
  static async processRequest(req, res) {
    try {
      const userId = req.user.userId;
      const { message, context } = req.body;

      if (!message) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Message is required'
        });
      }

      logger.info(`Processing spreadsheet agent request for user: ${userId}`);

      const result = await spreadsheetsAgentService.processRequest(userId, message, context);

      res.json(result);
    } catch (error) {
      logger.error('Error in processRequest:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while processing your request'
      });
    }
  }

  /**
   * Get all spreadsheets
   * GET /api/spreadsheets-agent/spreadsheets
   */
  static async getAllSpreadsheets(req, res) {
    try {
      const userId = req.user.userId;
      const options = req.query;

      logger.info(`Getting all spreadsheets for user: ${userId}`);

      const result = await spreadsheetsAgentService.getAllSpreadsheets(userId, options);

      res.json(result);
    } catch (error) {
      logger.error('Error in getAllSpreadsheets:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while fetching spreadsheets'
      });
    }
  }

  /**
   * Get spreadsheet details
   * GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId
   */
  static async getSpreadsheetDetails(req, res) {
    try {
      const userId = req.user.userId;
      const { spreadsheetId } = req.params;

      if (!spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Spreadsheet ID is required'
        });
      }

      logger.info(`Getting spreadsheet details for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      const result = await spreadsheetsAgentService.getSpreadsheetDetails(userId, spreadsheetId);

      res.json(result);
    } catch (error) {
      logger.error('Error in getSpreadsheetDetails:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while fetching spreadsheet details'
      });
    }
  }

  /**
   * Read spreadsheet content
   * GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content
   */
  static async readSpreadsheetContent(req, res) {
    try {
      const userId = req.user.userId;
      const { spreadsheetId } = req.params;
      const { sheetName, range } = req.query;

      if (!spreadsheetId) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Spreadsheet ID is required'
        });
      }

      logger.info(`Reading spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      const result = await spreadsheetsAgentService.readSpreadsheetContent(
        userId, 
        spreadsheetId, 
        sheetName, 
        range
      );

      res.json(result);
    } catch (error) {
      logger.error('Error in readSpreadsheetContent:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while reading spreadsheet content'
      });
    }
  }

  /**
   * Update spreadsheet content
   * PUT /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content
   */
  static async updateSpreadsheetContent(req, res) {
    try {
      const userId = req.user.userId;
      const { spreadsheetId } = req.params;
      const { sheetName, range, values } = req.body;

      if (!spreadsheetId || !sheetName || !range || !values) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Spreadsheet ID, sheet name, range, and values are required'
        });
      }

      if (!Array.isArray(values)) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Values must be an array'
        });
      }

      logger.info(`Updating spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      const result = await spreadsheetsAgentService.updateSpreadsheetContent(
        userId, 
        spreadsheetId, 
        sheetName, 
        range, 
        values
      );

      res.json(result);
    } catch (error) {
      logger.error('Error in updateSpreadsheetContent:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while updating spreadsheet content'
      });
    }
  }

  /**
   * Create new spreadsheet
   * POST /api/spreadsheets-agent/spreadsheets
   */
  static async createSpreadsheet(req, res) {
    try {
      const userId = req.user.userId;
      const { title, sheets, locale, timeZone } = req.body;

      if (!title) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Title is required'
        });
      }

      logger.info(`Creating spreadsheet for user: ${userId}, title: ${title}`);

      const options = {};
      if (sheets) options.sheets = sheets;
      if (locale) options.locale = locale;
      if (timeZone) options.timeZone = timeZone;

      const result = await spreadsheetsAgentService.createSpreadsheet(userId, title, options);

      res.json(result);
    } catch (error) {
      logger.error('Error in createSpreadsheet:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while creating spreadsheet'
      });
    }
  }

  /**
   * Get agent status
   * GET /api/spreadsheets-agent/status
   */
  static async getStatus(req, res) {
    try {
      const status = spreadsheetsAgentService.getStatus();
      res.json(status);
    } catch (error) {
      logger.error('Error in getStatus:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while fetching agent status'
      });
    }
  }

  /**
   * Get available operations
   * GET /api/spreadsheets-agent/operations
   */
  static async getAvailableOperations(req, res) {
    try {
      const operations = spreadsheetsAgentService.getAvailableOperations();
      res.json({
        success: true,
        operations
      });
    } catch (error) {
      logger.error('Error in getAvailableOperations:', error);
      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'An internal error occurred while fetching available operations'
      });
    }
  }
}
