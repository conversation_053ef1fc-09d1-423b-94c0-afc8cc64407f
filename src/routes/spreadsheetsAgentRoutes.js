/**
 * @fileoverview Routes for spreadsheet agent operations
 */

import express from 'express';
import { SpreadsheetsAgentController } from '../controllers/SpreadsheetsAgentController.js';
import { authenticateToken } from '../middleware/auth.js';
import { validateRequest } from '../middleware/validation.js';
import { rateLimiter } from '../middleware/rateLimiter.js';
import Joi from 'joi';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Apply rate limiting
router.use(rateLimiter);

/**
 * Validation schemas
 */
const processRequestSchema = Joi.object({
  message: Joi.string().required().min(1).max(2000),
  context: Joi.object().optional()
});

const updateContentSchema = Joi.object({
  sheetName: Joi.string().required(),
  range: Joi.string().required(),
  values: Joi.array().items(Joi.array()).required()
});

const createSpreadsheetSchema = Joi.object({
  title: Joi.string().required().min(1).max(255),
  sheets: Joi.array().items(
    Joi.object({
      title: Joi.string().required(),
      rowCount: Joi.number().integer().min(1).max(10000).optional(),
      columnCount: Joi.number().integer().min(1).max(1000).optional()
    })
  ).optional(),
  locale: Joi.string().optional(),
  timeZone: Joi.string().optional()
});

/**
 * @route POST /api/spreadsheets-agent/process
 * @desc Process natural language spreadsheet request
 * @access Private
 */
router.post('/process', 
  validateRequest(processRequestSchema),
  SpreadsheetsAgentController.processRequest
);

/**
 * @route GET /api/spreadsheets-agent/spreadsheets
 * @desc Get all spreadsheets for the user
 * @access Private
 */
router.get('/spreadsheets', 
  SpreadsheetsAgentController.getAllSpreadsheets
);

/**
 * @route GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId
 * @desc Get details of a specific spreadsheet
 * @access Private
 */
router.get('/spreadsheets/:spreadsheetId', 
  SpreadsheetsAgentController.getSpreadsheetDetails
);

/**
 * @route GET /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content
 * @desc Read content from a spreadsheet
 * @access Private
 * @query {string} sheetName - Optional sheet name
 * @query {string} range - Optional range (e.g., A1:C10)
 */
router.get('/spreadsheets/:spreadsheetId/content', 
  SpreadsheetsAgentController.readSpreadsheetContent
);

/**
 * @route PUT /api/spreadsheets-agent/spreadsheets/:spreadsheetId/content
 * @desc Update content in a spreadsheet
 * @access Private
 */
router.put('/spreadsheets/:spreadsheetId/content', 
  validateRequest(updateContentSchema),
  SpreadsheetsAgentController.updateSpreadsheetContent
);

/**
 * @route POST /api/spreadsheets-agent/spreadsheets
 * @desc Create a new spreadsheet
 * @access Private
 */
router.post('/spreadsheets', 
  validateRequest(createSpreadsheetSchema),
  SpreadsheetsAgentController.createSpreadsheet
);

/**
 * @route GET /api/spreadsheets-agent/status
 * @desc Get agent status and health check
 * @access Private
 */
router.get('/status', 
  SpreadsheetsAgentController.getStatus
);

/**
 * @route GET /api/spreadsheets-agent/operations
 * @desc Get available operations
 * @access Private
 */
router.get('/operations', 
  SpreadsheetsAgentController.getAvailableOperations
);

export default router;
