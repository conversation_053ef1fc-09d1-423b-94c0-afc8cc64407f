import { GoogleAuthService } from './GoogleAuthService.js';
import { logger } from '../utils/logger.js';

/**
 * DriveUtilityApp Class
 * Provides utility methods for working with Google Sheets API
 * Handles spreadsheet creation, content management, and data retrieval
 */
export class DriveUtilityApp {
  /**
   * Create a new Google Sheets spreadsheet
   * @param {string} userId - User ID for authentication
   * @param {string} title - Title of the spreadsheet
   * @param {Object} options - Optional configuration
   * @param {Array} options.sheets - Initial sheets configuration
   * @param {string} options.locale - Spreadsheet locale (default: 'en_US')
   * @param {string} options.timeZone - Spreadsheet timezone (default: 'America/New_York')
   * @returns {Promise<Object>} Created spreadsheet information
   */
  static async createSpreadsheet(userId, title, options = {}) {
    try {
      logger.info(`Creating spreadsheet for user: ${userId}, title: ${title}`);

      if (!title || typeof title !== 'string') {
        throw new Error('Spreadsheet title is required and must be a string');
      }

      const sheetsClient = await this._getSheetsClient(userId);

      // Prepare spreadsheet properties
      const spreadsheetProperties = {
        title: title.trim(),
        locale: options.locale || 'en_US',
        timeZone: options.timeZone || 'America/New_York'
      };

      // Prepare initial sheets configuration
      const sheets = [];
      if (options.sheets && Array.isArray(options.sheets)) {
        options.sheets.forEach((sheetConfig, index) => {
          sheets.push({
            properties: {
              title: sheetConfig.title || `Sheet${index + 1}`,
              sheetType: 'GRID',
              gridProperties: {
                rowCount: sheetConfig.rowCount || 1000,
                columnCount: sheetConfig.columnCount || 26
              }
            }
          });
        });
      } else {
        // Default sheet
        sheets.push({
          properties: {
            title: 'Sheet1',
            sheetType: 'GRID',
            gridProperties: {
              rowCount: 1000,
              columnCount: 26
            }
          }
        });
      }

      // Create the spreadsheet
      const request = {
        resource: {
          properties: spreadsheetProperties,
          sheets: sheets
        }
      };

      const response = await sheetsClient.spreadsheets.create(request);

      logger.info(`Spreadsheet created successfully: ${response.data.spreadsheetId}`);

      return {
        success: true,
        spreadsheetId: response.data.spreadsheetId,
        spreadsheetUrl: response.data.spreadsheetUrl,
        title: response.data.properties.title,
        sheets: response.data.sheets.map(sheet => ({
          sheetId: sheet.properties.sheetId,
          title: sheet.properties.title,
          index: sheet.properties.index,
          sheetType: sheet.properties.sheetType
        })),
        properties: response.data.properties
      };

    } catch (error) {
      logger.error('Error creating spreadsheet:', error);
      throw this._handleGoogleApiError(error, 'spreadsheet creation');
    }
  }

  /**
   * Update content in a Google Sheets spreadsheet
   * @param {string} userId - User ID for authentication
   * @param {string} spreadsheetId - ID of the spreadsheet
   * @param {string} sheetName - Name of the sheet to update
   * @param {string} range - Range to update (e.g., 'A1:C3')
   * @param {Array<Array>} values - 2D array of values to update
   * @param {Object} options - Optional configuration
   * @param {string} options.valueInputOption - How values should be interpreted ('RAW' or 'USER_ENTERED')
   * @param {boolean} options.includeValuesInResponse - Whether to include updated values in response
   * @returns {Promise<Object>} Update result information
   */
  static async updateSpreadsheetContent(userId, spreadsheetId, sheetName, range, values, options = {}) {
    try {
      logger.info(`Updating spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      // Validate inputs
      this._validateSpreadsheetId(spreadsheetId);

      if (!sheetName || typeof sheetName !== 'string') {
        throw new Error('Sheet name is required and must be a string');
      }

      if (!range || typeof range !== 'string') {
        throw new Error('Range is required and must be a string');
      }

      this._validateValues(values);

      const sheetsClient = await this._getSheetsClient(userId);

      // Construct the full range with sheet name
      const fullRange = range.includes('!') ? range : `${sheetName}!${range}`;
      this._validateRange(fullRange);

      // Prepare update request
      const valueInputOption = options.valueInputOption || 'USER_ENTERED';
      const includeValuesInResponse = options.includeValuesInResponse || false;

      const request = {
        spreadsheetId: spreadsheetId,
        range: fullRange,
        valueInputOption: valueInputOption,
        includeValuesInResponse: includeValuesInResponse,
        resource: {
          values: values
        }
      };

      const response = await sheetsClient.spreadsheets.values.update(request);

      logger.info(`Spreadsheet content updated successfully: ${response.data.updatedCells} cells updated`);

      return {
        success: true,
        spreadsheetId: spreadsheetId,
        updatedRange: response.data.updatedRange,
        updatedRows: response.data.updatedRows,
        updatedColumns: response.data.updatedColumns,
        updatedCells: response.data.updatedCells,
        updatedData: response.data.updatedData || null
      };

    } catch (error) {
      logger.error('Error updating spreadsheet content:', error);
      throw this._handleGoogleApiError(error, 'spreadsheet content update');
    }
  }

  /**
   * Read content from a Google Sheets spreadsheet
   * @param {string} userId - User ID for authentication
   * @param {string} spreadsheetId - ID of the spreadsheet
   * @param {Object} options - Optional configuration
   * @param {string} options.sheetName - Specific sheet name to read (if not provided, reads all sheets)
   * @param {string} options.range - Specific range to read (e.g., 'A1:C10')
   * @param {string} options.valueRenderOption - How values should be rendered ('FORMATTED_VALUE', 'UNFORMATTED_VALUE', 'FORMULA')
   * @param {string} options.dateTimeRenderOption - How dates should be rendered ('SERIAL_NUMBER', 'FORMATTED_STRING')
   * @returns {Promise<Object>} Spreadsheet content data
   */
  static async readSpreadsheetContent(userId, spreadsheetId, options = {}) {
    try {
      logger.info(`Reading spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      // Validate inputs
      this._validateSpreadsheetId(spreadsheetId);

      const sheetsClient = await this._getSheetsClient(userId);

      const valueRenderOption = options.valueRenderOption || 'FORMATTED_VALUE';
      const dateTimeRenderOption = options.dateTimeRenderOption || 'FORMATTED_STRING';

      // If specific sheet and range are provided
      if (options.sheetName && options.range) {
        const fullRange = options.range.includes('!') ? options.range : `${options.sheetName}!${options.range}`;
        this._validateRange(fullRange);

        const request = {
          spreadsheetId: spreadsheetId,
          range: fullRange,
          valueRenderOption: valueRenderOption,
          dateTimeRenderOption: dateTimeRenderOption
        };

        const response = await sheetsClient.spreadsheets.values.get(request);

        return {
          success: true,
          spreadsheetId: spreadsheetId,
          range: response.data.range,
          majorDimension: response.data.majorDimension,
          values: response.data.values || [],
          sheetData: [{
            sheetName: options.sheetName,
            range: response.data.range,
            values: response.data.values || []
          }]
        };
      }

      // If specific sheet is provided but no range
      if (options.sheetName) {
        const request = {
          spreadsheetId: spreadsheetId,
          range: options.sheetName,
          valueRenderOption: valueRenderOption,
          dateTimeRenderOption: dateTimeRenderOption
        };

        const response = await sheetsClient.spreadsheets.values.get(request);

        return {
          success: true,
          spreadsheetId: spreadsheetId,
          range: response.data.range,
          majorDimension: response.data.majorDimension,
          values: response.data.values || [],
          sheetData: [{
            sheetName: options.sheetName,
            range: response.data.range,
            values: response.data.values || []
          }]
        };
      }

      // Read all sheets
      const spreadsheetInfo = await sheetsClient.spreadsheets.get({
        spreadsheetId: spreadsheetId
      });

      const sheets = spreadsheetInfo.data.sheets;
      const sheetData = [];

      for (const sheet of sheets) {
        const sheetName = sheet.properties.title;

        try {
          const request = {
            spreadsheetId: spreadsheetId,
            range: sheetName,
            valueRenderOption: valueRenderOption,
            dateTimeRenderOption: dateTimeRenderOption
          };

          const response = await sheetsClient.spreadsheets.values.get(request);

          sheetData.push({
            sheetName: sheetName,
            sheetId: sheet.properties.sheetId,
            range: response.data.range,
            values: response.data.values || [],
            properties: sheet.properties
          });
        } catch (sheetError) {
          logger.warn(`Error reading sheet ${sheetName}:`, sheetError.message);
          sheetData.push({
            sheetName: sheetName,
            sheetId: sheet.properties.sheetId,
            error: sheetError.message,
            values: []
          });
        }
      }

      return {
        success: true,
        spreadsheetId: spreadsheetId,
        title: spreadsheetInfo.data.properties.title,
        sheetData: sheetData,
        totalSheets: sheets.length
      };

    } catch (error) {
      logger.error('Error reading spreadsheet content:', error);
      throw this._handleGoogleApiError(error, 'spreadsheet content reading');
    }
  }

  /**
   * Get all Google Sheets spreadsheets accessible to the user
   * @param {string} userId - User ID for authentication
   * @param {Object} options - Optional configuration
   * @param {number} options.pageSize - Number of files to return per page (default: 100)
   * @param {string} options.orderBy - How to order the results (default: 'modifiedTime desc')
   * @returns {Promise<Object>} List of spreadsheets
   */
  static async getAllSpreadsheets(userId, options = {}) {
    try {
      logger.info(`Getting all spreadsheets for user: ${userId}`);

      // Validate user permissions
      await this._validateSheetsPermissions(userId);

      // Get Google Drive API client
      const driveClient = await GoogleAuthService.getApiClient(userId, 'drive');

      const pageSize = options.pageSize || 100;
      const orderBy = options.orderBy || 'modifiedTime desc';

      // Query for Google Sheets files
      const query = "mimeType='application/vnd.google-apps.spreadsheet' and trashed=false";

      const response = await driveClient.files.list({
        q: query,
        pageSize: pageSize,
        orderBy: orderBy,
        fields: 'nextPageToken, files(id, name, createdTime, modifiedTime, owners, shared, webViewLink, size)'
      });

      const spreadsheets = response.data.files.map(file => ({
        id: file.id,
        name: file.name,
        createdTime: file.createdTime,
        modifiedTime: file.modifiedTime,
        owners: file.owners,
        shared: file.shared,
        webViewLink: file.webViewLink,
        size: file.size
      }));

      return {
        success: true,
        spreadsheets: spreadsheets,
        totalCount: spreadsheets.length,
        nextPageToken: response.data.nextPageToken || null
      };
    } catch (error) {
      logger.error('Error getting all spreadsheets:', error);
      const handledError = this._handleApiError(error);
      throw handledError;
    }
  }

  /**
   * Get all available sheets in a spreadsheet
   * @param {string} userId - User ID for authentication
   * @param {string} spreadsheetId - ID of the spreadsheet
   * @returns {Promise<Array>} Array of sheet information
   */
  static async getAvailableSheets(userId, spreadsheetId) {
    try {
      logger.info(`Getting available sheets for user: ${userId}, spreadsheet: ${spreadsheetId}`);

      // Validate inputs
      this._validateSpreadsheetId(spreadsheetId);

      const sheetsClient = await this._getSheetsClient(userId);

      // Get spreadsheet metadata
      const response = await sheetsClient.spreadsheets.get({
        spreadsheetId: spreadsheetId,
        fields: 'properties,sheets.properties'
      });

      const sheets = response.data.sheets.map(sheet => {
        const properties = sheet.properties;
        return {
          sheetId: properties.sheetId,
          title: properties.title,
          index: properties.index,
          sheetType: properties.sheetType,
          hidden: properties.hidden || false,
          rightToLeft: properties.rightToLeft || false,
          tabColor: properties.tabColor || null,
          gridProperties: properties.gridProperties ? {
            rowCount: properties.gridProperties.rowCount,
            columnCount: properties.gridProperties.columnCount,
            frozenRowCount: properties.gridProperties.frozenRowCount || 0,
            frozenColumnCount: properties.gridProperties.frozenColumnCount || 0,
            hideGridlines: properties.gridProperties.hideGridlines || false
          } : null
        };
      });

      logger.info(`Found ${sheets.length} sheets in spreadsheet: ${spreadsheetId}`);

      return {
        success: true,
        spreadsheetId: spreadsheetId,
        spreadsheetTitle: response.data.properties.title,
        sheets: sheets,
        totalSheets: sheets.length
      };

    } catch (error) {
      logger.error('Error getting available sheets:', error);
      throw this._handleGoogleApiError(error, 'getting available sheets');
    }
  }

  /**
   * Get detailed data about a specific sheet
   * @param {string} userId - User ID for authentication
   * @param {string} spreadsheetId - ID of the spreadsheet
   * @param {string} sheetName - Name of the sheet
   * @returns {Promise<Object>} Detailed sheet information
   */
  static async getSheetData(userId, spreadsheetId, sheetName) {
    try {
      logger.info(`Getting sheet data for user: ${userId}, spreadsheet: ${spreadsheetId}, sheet: ${sheetName}`);

      // Validate inputs
      this._validateSpreadsheetId(spreadsheetId);

      if (!sheetName || typeof sheetName !== 'string') {
        throw new Error('Sheet name is required and must be a string');
      }

      const sheetsClient = await this._getSheetsClient(userId);

      // Get spreadsheet metadata to find the specific sheet
      const spreadsheetResponse = await sheetsClient.spreadsheets.get({
        spreadsheetId: spreadsheetId,
        fields: 'properties,sheets.properties'
      });

      // Find the specific sheet
      const targetSheet = spreadsheetResponse.data.sheets.find(
        sheet => sheet.properties.title === sheetName
      );

      if (!targetSheet) {
        throw new Error(`Sheet '${sheetName}' not found in spreadsheet`);
      }

      // Get sheet content
      const valuesResponse = await sheetsClient.spreadsheets.values.get({
        spreadsheetId: spreadsheetId,
        range: sheetName,
        valueRenderOption: 'FORMATTED_VALUE',
        dateTimeRenderOption: 'FORMATTED_STRING'
      });

      const properties = targetSheet.properties;
      const values = valuesResponse.data.values || [];

      // Calculate actual data dimensions
      const actualRowCount = values.length;
      const actualColumnCount = values.length > 0 ? Math.max(...values.map(row => row.length)) : 0;

      // Get additional sheet metadata if available
      const sheetData = {
        sheetId: properties.sheetId,
        title: properties.title,
        index: properties.index,
        sheetType: properties.sheetType,
        hidden: properties.hidden || false,
        rightToLeft: properties.rightToLeft || false,
        tabColor: properties.tabColor || null,
        gridProperties: properties.gridProperties ? {
          rowCount: properties.gridProperties.rowCount,
          columnCount: properties.gridProperties.columnCount,
          frozenRowCount: properties.gridProperties.frozenRowCount || 0,
          frozenColumnCount: properties.gridProperties.frozenColumnCount || 0,
          hideGridlines: properties.gridProperties.hideGridlines || false
        } : null,
        actualDataDimensions: {
          rowCount: actualRowCount,
          columnCount: actualColumnCount,
          hasData: actualRowCount > 0 && actualColumnCount > 0
        },
        values: values,
        range: valuesResponse.data.range,
        majorDimension: valuesResponse.data.majorDimension
      };

      logger.info(`Retrieved data for sheet '${sheetName}': ${actualRowCount} rows, ${actualColumnCount} columns`);

      return {
        success: true,
        spreadsheetId: spreadsheetId,
        spreadsheetTitle: spreadsheetResponse.data.properties.title,
        sheetData: sheetData
      };

    } catch (error) {
      logger.error('Error getting sheet data:', error);
      throw this._handleGoogleApiError(error, 'getting sheet data');
    }
  }

  /**
   * Validate user has required Google Sheets permissions
   * @param {string} userId - User ID for authentication
   * @returns {Promise<boolean>} True if user has required permissions
   * @private
   */
  static async _validateSheetsPermissions(userId) {
    try {
      if (!userId || typeof userId !== 'string') {
        throw new Error('User ID is required and must be a string');
      }

      const hasScopes = await GoogleAuthService.hasRequiredScopes(userId, [
        GoogleAuthService.SCOPES.SHEETS
      ]);

      if (!hasScopes) {
        throw new Error('User has not granted Google Sheets access. Please authenticate with Google Sheets permissions.');
      }

      return true;
    } catch (error) {
      logger.error('Error validating Sheets permissions:', error);

      // Provide more specific error messages
      if (error.message.includes('not authenticated')) {
        throw new Error('User is not authenticated with Google. Please complete Google OAuth authentication first.');
      }

      if (error.message.includes('expired')) {
        throw new Error('Google authentication has expired. Please re-authenticate with Google.');
      }

      throw error;
    }
  }

  /**
   * Get authenticated Google Sheets API client
   * @param {string} userId - User ID for authentication
   * @returns {Promise<Object>} Google Sheets API client
   * @private
   */
  static async _getSheetsClient(userId) {
    try {
      await this._validateSheetsPermissions(userId);
      return await GoogleAuthService.getApiClient(userId, 'sheets');
    } catch (error) {
      logger.error('Error getting Sheets client:', error);
      throw error;
    }
  }

  /**
   * Validate spreadsheet ID format
   * @param {string} spreadsheetId - Spreadsheet ID to validate
   * @returns {boolean} True if valid
   * @private
   */
  static _validateSpreadsheetId(spreadsheetId) {
    if (!spreadsheetId || typeof spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID is required and must be a string');
    }

    if (spreadsheetId.trim().length === 0) {
      throw new Error('Spreadsheet ID cannot be empty');
    }

    // Google Sheets ID is typically 44 characters long and contains alphanumeric characters, hyphens, and underscores
    const spreadsheetIdRegex = /^[a-zA-Z0-9-_]{10,}$/;
    if (!spreadsheetIdRegex.test(spreadsheetId.trim())) {
      throw new Error('Invalid spreadsheet ID format. Expected alphanumeric characters, hyphens, and underscores with minimum 10 characters.');
    }

    return true;
  }

  /**
   * Validate range format
   * @param {string} range - Range to validate (e.g., 'A1:C3', 'Sheet1!A1:C3')
   * @returns {boolean} True if valid
   * @private
   */
  static _validateRange(range) {
    if (!range || typeof range !== 'string') {
      throw new Error('Range is required and must be a string');
    }

    if (range.trim().length === 0) {
      throw new Error('Range cannot be empty');
    }

    const trimmedRange = range.trim();

    // Allow sheet names only (e.g., 'Sheet1')
    const sheetNameOnlyRegex = /^[a-zA-Z0-9\s']+$/;
    if (sheetNameOnlyRegex.test(trimmedRange) && !trimmedRange.includes(':') && !trimmedRange.includes('!')) {
      return true;
    }

    // Comprehensive range validation patterns
    const rangePatterns = [
      // Cell range: A1:C3, Sheet1!A1:C3
      /^([a-zA-Z0-9\s']+!)?[A-Z]+[0-9]+:[A-Z]+[0-9]+$/,
      // Column range: A:C, Sheet1!A:C
      /^([a-zA-Z0-9\s']+!)?[A-Z]+:[A-Z]+$/,
      // Row range: 1:3, Sheet1!1:3
      /^([a-zA-Z0-9\s']+!)?[0-9]+:[0-9]+$/,
      // Single cell: A1, Sheet1!A1
      /^([a-zA-Z0-9\s']+!)?[A-Z]+[0-9]+$/,
      // Sheet with range: 'Sheet Name'!A1:C3
      /^'[^']+'\![A-Z]+[0-9]+:[A-Z]+[0-9]+$/,
      // Sheet name only with exclamation: Sheet1!
      /^[a-zA-Z0-9\s']+!$/
    ];

    const isValid = rangePatterns.some(pattern => pattern.test(trimmedRange));

    if (!isValid) {
      throw new Error('Invalid range format. Use formats like "A1:C3", "Sheet1!A1:C3", "A:C", "1:3", "A1", or sheet names like "Sheet1"');
    }

    return true;
  }

  /**
   * Handle Google API errors and provide user-friendly messages
   * @param {Error} error - Original error from Google API
   * @param {string} operation - Operation being performed
   * @returns {Error} Enhanced error with user-friendly message
   * @private
   */
  static _handleGoogleApiError(error, operation) {
    logger.error(`Google API error during ${operation}:`, error);

    // Handle specific Google API error codes
    if (error.code) {
      switch (error.code) {
        case 400:
          return new Error(`Invalid request for ${operation}. Please check your parameters.`);
        case 401:
          return new Error('Authentication failed. Please re-authenticate with Google.');
        case 403:
          if (error.message.includes('insufficient permissions')) {
            return new Error('Insufficient permissions. Please grant Google Sheets access.');
          }
          if (error.message.includes('quota')) {
            return new Error('Google API quota exceeded. Please try again later.');
          }
          return new Error('Access denied. Please check your permissions.');
        case 404:
          return new Error('Spreadsheet or sheet not found. Please verify the ID and try again.');
        case 429:
          return new Error('Too many requests. Please wait a moment and try again.');
        case 500:
        case 502:
        case 503:
          return new Error('Google Sheets service is temporarily unavailable. Please try again later.');
        default:
          return new Error(`Google API error (${error.code}): ${error.message}`);
      }
    }

    // Handle network and other errors
    if (error.message.includes('ENOTFOUND') || error.message.includes('network')) {
      return new Error('Network error. Please check your internet connection and try again.');
    }

    if (error.message.includes('timeout')) {
      return new Error('Request timeout. Please try again.');
    }

    // Return original error if no specific handling
    return error;
  }

  /**
   * Validate values array for spreadsheet updates
   * @param {Array} values - 2D array of values
   * @returns {boolean} True if valid
   * @private
   */
  static _validateValues(values) {
    if (!Array.isArray(values)) {
      throw new Error('Values must be an array');
    }

    if (values.length === 0) {
      throw new Error('Values array cannot be empty');
    }

    // Check if it's a 2D array
    for (let i = 0; i < values.length; i++) {
      if (!Array.isArray(values[i])) {
        throw new Error(`Row ${i} must be an array. Expected 2D array format.`);
      }
    }

    return true;
  }
}
