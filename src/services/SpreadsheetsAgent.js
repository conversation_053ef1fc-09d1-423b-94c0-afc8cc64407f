/**
 * @fileoverview SpreadsheetsAgent - Agentic workflow for Google Sheets operations using LangGraph
 */

import { StateGraph, START, END } from '@langchain/langgraph';
import { HumanMessage, AIMessage, SystemMessage } from '@langchain/core/messages';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { DriveUtilityApp } from './DriveUtilityApp.js';
import { LLMService } from './LLMService.js';
import logger from '../config/logger.js';

/**
 * Agent State Schema
 */
const AgentState = z.object({
  messages: z.array(z.any()).default([]),
  userId: z.string(),
  currentAction: z.string().optional(),
  spreadsheetId: z.string().optional(),
  sheetName: z.string().optional(),
  range: z.string().optional(),
  data: z.any().optional(),
  error: z.string().optional(),
  result: z.any().optional(),
  toolCalls: z.array(z.any()).default([]),
  isComplete: z.boolean().default(false)
});

/**
 * SpreadsheetsAgent Class
 * Provides agentic workflow for Google Sheets operations using LangGraph
 */
export class SpreadsheetsAgent {
  constructor() {
    this.graph = null;
    this.tools = this._createTools();
    this._initializeGraph();
  }

  /**
   * Create LangGraph tools that utilize DriveUtilityApp methods
   * @returns {Array} Array of LangGraph tools
   * @private
   */
  _createTools() {
    // Tool 1: Get all spreadsheets (Note: DriveUtilityApp doesn't have this method, we'll need to implement it)
    const getAllSpreadsheetsSchema = z.object({
      userId: z.string().describe('User ID for authentication')
    });

    const getAllSpreadsheetsTool = tool(
      async ({ userId }) => {
        try {
          logger.info(`Getting all spreadsheets for user: ${userId}`);
          const result = await DriveUtilityApp.getAllSpreadsheets(userId);
          return {
            success: true,
            spreadsheets: result.spreadsheets,
            totalCount: result.totalCount
          };
        } catch (error) {
          logger.error('Error getting all spreadsheets:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      {
        name: 'get_all_spreadsheets',
        description: 'Get all Google Sheets spreadsheets accessible to the user',
        schema: getAllSpreadsheetsSchema
      }
    );

    // Tool 2: Get details of a specific spreadsheet
    const getSpreadsheetDetailsSchema = z.object({
      userId: z.string().describe('User ID for authentication'),
      spreadsheetId: z.string().describe('ID of the spreadsheet to get details for')
    });

    const getSpreadsheetDetailsTool = tool(
      async ({ userId, spreadsheetId }) => {
        try {
          logger.info(`Getting spreadsheet details for user: ${userId}, spreadsheet: ${spreadsheetId}`);
          const result = await DriveUtilityApp.getAvailableSheets(userId, spreadsheetId);
          return {
            success: true,
            spreadsheetDetails: result
          };
        } catch (error) {
          logger.error('Error getting spreadsheet details:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      {
        name: 'get_spreadsheet_details',
        description: 'Get detailed information about a specific spreadsheet including all sheets',
        schema: getSpreadsheetDetailsSchema
      }
    );

    // Tool 3: Read content from a spreadsheet
    const readSpreadsheetContentSchema = z.object({
      userId: z.string().describe('User ID for authentication'),
      spreadsheetId: z.string().describe('ID of the spreadsheet to read from'),
      sheetName: z.string().optional().describe('Specific sheet name to read (optional)'),
      range: z.string().optional().describe('Specific range to read (e.g., A1:C10, optional)')
    });

    const readSpreadsheetContentTool = tool(
      async ({ userId, spreadsheetId, sheetName, range }) => {
        try {
          logger.info(`Reading spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);
          const options = {};
          if (sheetName) options.sheetName = sheetName;
          if (range) options.range = range;
          
          const result = await DriveUtilityApp.readSpreadsheetContent(userId, spreadsheetId, options);
          return {
            success: true,
            content: result
          };
        } catch (error) {
          logger.error('Error reading spreadsheet content:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      {
        name: 'read_spreadsheet_content',
        description: 'Read content from a Google Sheets spreadsheet',
        schema: readSpreadsheetContentSchema
      }
    );

    // Tool 4: Update content in a spreadsheet
    const updateSpreadsheetContentSchema = z.object({
      userId: z.string().describe('User ID for authentication'),
      spreadsheetId: z.string().describe('ID of the spreadsheet to update'),
      sheetName: z.string().describe('Name of the sheet to update'),
      range: z.string().describe('Range to update (e.g., A1:C3)'),
      values: z.array(z.array(z.any())).describe('2D array of values to update')
    });

    const updateSpreadsheetContentTool = tool(
      async ({ userId, spreadsheetId, sheetName, range, values }) => {
        try {
          logger.info(`Updating spreadsheet content for user: ${userId}, spreadsheet: ${spreadsheetId}`);
          const result = await DriveUtilityApp.updateSpreadsheetContent(
            userId, 
            spreadsheetId, 
            sheetName, 
            range, 
            values
          );
          return {
            success: true,
            updateResult: result
          };
        } catch (error) {
          logger.error('Error updating spreadsheet content:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      {
        name: 'update_spreadsheet_content',
        description: 'Update content in a Google Sheets spreadsheet',
        schema: updateSpreadsheetContentSchema
      }
    );

    // Tool 5: Create a new spreadsheet
    const createSpreadsheetSchema = z.object({
      userId: z.string().describe('User ID for authentication'),
      title: z.string().describe('Title of the new spreadsheet'),
      sheets: z.array(z.object({
        title: z.string(),
        rowCount: z.number().optional(),
        columnCount: z.number().optional()
      })).optional().describe('Initial sheets configuration'),
      locale: z.string().optional().describe('Spreadsheet locale'),
      timeZone: z.string().optional().describe('Spreadsheet timezone')
    });

    const createSpreadsheetTool = tool(
      async ({ userId, title, sheets, locale, timeZone }) => {
        try {
          logger.info(`Creating new spreadsheet for user: ${userId}, title: ${title}`);
          const options = {};
          if (sheets) options.sheets = sheets;
          if (locale) options.locale = locale;
          if (timeZone) options.timeZone = timeZone;
          
          const result = await DriveUtilityApp.createSpreadsheet(userId, title, options);
          return {
            success: true,
            spreadsheet: result
          };
        } catch (error) {
          logger.error('Error creating spreadsheet:', error);
          return {
            success: false,
            error: error.message
          };
        }
      },
      {
        name: 'create_spreadsheet',
        description: 'Create a new Google Sheets spreadsheet',
        schema: createSpreadsheetSchema
      }
    );

    return [
      getAllSpreadsheetsTool,
      getSpreadsheetDetailsTool,
      readSpreadsheetContentTool,
      updateSpreadsheetContentTool,
      createSpreadsheetTool
    ];
  }

  /**
   * Initialize the LangGraph workflow
   * @private
   */
  _initializeGraph() {
    const workflow = new StateGraph(AgentState);

    // Add nodes
    workflow.addNode('agent', this._agentNode.bind(this));
    workflow.addNode('tools', this._toolsNode.bind(this));
    workflow.addNode('respond', this._respondNode.bind(this));

    // Add edges
    workflow.addEdge(START, 'agent');
    workflow.addConditionalEdges(
      'agent',
      this._shouldContinue.bind(this),
      {
        continue: 'tools',
        end: 'respond'
      }
    );
    workflow.addEdge('tools', 'agent');
    workflow.addEdge('respond', END);

    this.graph = workflow.compile();
  }

  /**
   * Agent node - decides what action to take
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   * @private
   */
  async _agentNode(state) {
    try {
      const lastMessage = state.messages[state.messages.length - 1];

      // If there are tool calls to process, return them
      if (state.toolCalls && state.toolCalls.length > 0) {
        return {
          ...state,
          currentAction: 'executing_tools'
        };
      }

      // System prompt for the agent
      const systemPrompt = `You are a Google Sheets assistant agent. You can help users with:
1. Getting all their spreadsheets
2. Getting details about specific spreadsheets
3. Reading content from spreadsheets
4. Updating content in spreadsheets
5. Creating new spreadsheets

Available tools:
- get_all_spreadsheets: Get all user's spreadsheets
- get_spreadsheet_details: Get details about a specific spreadsheet
- read_spreadsheet_content: Read content from a spreadsheet
- update_spreadsheet_content: Update content in a spreadsheet
- create_spreadsheet: Create a new spreadsheet

Analyze the user's request and determine which tool(s) to use. If you need to use tools, respond with tool calls. If you can answer directly, provide a helpful response.

User ID: ${state.userId}`;

      // Get LLM response with tool calling capability
      const model = await LLMService.getModelWithDatabaseApiKey('gpt-4o');
      const modelWithTools = model.bindTools(this.tools);

      const messages = [
        new SystemMessage(systemPrompt),
        ...state.messages
      ];

      const response = await modelWithTools.invoke(messages);

      // Check if the model wants to use tools
      if (response.tool_calls && response.tool_calls.length > 0) {
        return {
          ...state,
          messages: [...state.messages, response],
          toolCalls: response.tool_calls,
          currentAction: 'tool_calling'
        };
      }

      // No tools needed, prepare final response
      return {
        ...state,
        messages: [...state.messages, response],
        result: response.content,
        isComplete: true,
        currentAction: 'responding'
      };
    } catch (error) {
      logger.error('Error in agent node:', error);
      return {
        ...state,
        error: error.message,
        isComplete: true,
        currentAction: 'error'
      };
    }
  }

  /**
   * Tools node - executes the selected tool
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   * @private
   */
  async _toolsNode(state) {
    try {
      const toolResults = [];

      for (const toolCall of state.toolCalls) {
        const tool = this.tools.find(t => t.name === toolCall.name);
        if (tool) {
          logger.info(`Executing tool: ${toolCall.name} with args:`, toolCall.args);
          const result = await tool.invoke(toolCall.args);
          toolResults.push({
            tool_call_id: toolCall.id,
            name: toolCall.name,
            result: result
          });
        } else {
          logger.warn(`Tool not found: ${toolCall.name}`);
          toolResults.push({
            tool_call_id: toolCall.id,
            name: toolCall.name,
            result: { success: false, error: `Tool ${toolCall.name} not found` }
          });
        }
      }

      // Create tool messages for the conversation
      const toolMessages = toolResults.map(result => ({
        type: 'tool',
        tool_call_id: result.tool_call_id,
        name: result.name,
        content: JSON.stringify(result.result)
      }));

      return {
        ...state,
        messages: [...state.messages, ...toolMessages],
        toolCalls: [],
        result: toolResults,
        currentAction: 'tools_executed'
      };
    } catch (error) {
      logger.error('Error in tools node:', error);
      return {
        ...state,
        error: error.message,
        toolCalls: [],
        currentAction: 'error'
      };
    }
  }

  /**
   * Respond node - formats the final response
   * @param {Object} state - Current state
   * @returns {Object} Updated state
   * @private
   */
  async _respondNode(state) {
    try {
      // If there's an error, return error response
      if (state.error) {
        return {
          ...state,
          result: {
            success: false,
            error: state.error,
            message: 'An error occurred while processing your request.'
          },
          isComplete: true
        };
      }

      // If we have a direct result from agent, return it
      if (state.result && typeof state.result === 'string') {
        return {
          ...state,
          result: {
            success: true,
            message: state.result,
            data: null
          },
          isComplete: true
        };
      }

      // If we have tool results, format them nicely
      if (state.result && Array.isArray(state.result)) {
        const successfulResults = state.result.filter(r => r.result.success);
        const failedResults = state.result.filter(r => !r.result.success);

        let message = 'I have completed your spreadsheet operations:\n\n';

        successfulResults.forEach(result => {
          message += `✅ ${result.name}: Success\n`;
          if (result.result.spreadsheet) {
            message += `   Created spreadsheet: ${result.result.spreadsheet.title}\n`;
          }
          if (result.result.content) {
            message += `   Retrieved data from spreadsheet\n`;
          }
          if (result.result.updateResult) {
            message += `   Updated spreadsheet successfully\n`;
          }
        });

        if (failedResults.length > 0) {
          message += '\n❌ Some operations failed:\n';
          failedResults.forEach(result => {
            message += `   ${result.name}: ${result.result.error}\n`;
          });
        }

        return {
          ...state,
          result: {
            success: failedResults.length === 0,
            message: message.trim(),
            data: state.result
          },
          isComplete: true
        };
      }

      // Default response
      return {
        ...state,
        result: {
          success: true,
          message: 'Request processed successfully.',
          data: state.result
        },
        isComplete: true
      };
    } catch (error) {
      logger.error('Error in respond node:', error);
      return {
        ...state,
        result: {
          success: false,
          error: error.message,
          message: 'An error occurred while formatting the response.'
        },
        isComplete: true
      };
    }
  }

  /**
   * Conditional edge function to determine next step
   * @param {Object} state - Current state
   * @returns {string} Next step ('continue' or 'end')
   * @private
   */
  _shouldContinue(state) {
    // If there are tool calls to execute, continue to tools
    if (state.toolCalls && state.toolCalls.length > 0) {
      return 'continue';
    }

    // If the agent is complete or there's an error, end
    if (state.isComplete || state.error) {
      return 'end';
    }

    // If tools were just executed, continue back to agent for final response
    if (state.currentAction === 'tools_executed') {
      return 'continue';
    }

    // Default to end
    return 'end';
  }

  /**
   * Validate input parameters
   * @param {string} userId - User ID
   * @param {string} message - User message
   * @param {Object} context - Additional context
   * @private
   */
  _validateInput(userId, message, context = {}) {
    if (!userId || typeof userId !== 'string') {
      throw new Error('User ID is required and must be a string');
    }

    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      throw new Error('Message is required and must be a non-empty string');
    }

    if (context && typeof context !== 'object') {
      throw new Error('Context must be an object');
    }

    // Validate specific context parameters if provided
    if (context.spreadsheetId && typeof context.spreadsheetId !== 'string') {
      throw new Error('Spreadsheet ID must be a string');
    }

    if (context.sheetName && typeof context.sheetName !== 'string') {
      throw new Error('Sheet name must be a string');
    }

    if (context.range && typeof context.range !== 'string') {
      throw new Error('Range must be a string');
    }
  }

  /**
   * Handle and categorize errors
   * @param {Error} error - The error to handle
   * @returns {Object} Formatted error response
   * @private
   */
  _handleError(error) {
    logger.error('SpreadsheetsAgent error:', error);

    // Authentication errors
    if (error.message.includes('not authenticated') || error.message.includes('authentication')) {
      return {
        success: false,
        error: 'AUTHENTICATION_ERROR',
        message: 'User is not authenticated with Google. Please authenticate first.',
        details: error.message
      };
    }

    // Permission errors
    if (error.message.includes('permission') || error.message.includes('access')) {
      return {
        success: false,
        error: 'PERMISSION_ERROR',
        message: 'Insufficient permissions. Please grant Google Sheets access.',
        details: error.message
      };
    }

    // Validation errors
    if (error.message.includes('required') || error.message.includes('invalid')) {
      return {
        success: false,
        error: 'VALIDATION_ERROR',
        message: 'Invalid input parameters.',
        details: error.message
      };
    }

    // API errors
    if (error.message.includes('API') || error.message.includes('quota')) {
      return {
        success: false,
        error: 'API_ERROR',
        message: 'Google Sheets API error. Please try again later.',
        details: error.message
      };
    }

    // Network errors
    if (error.message.includes('network') || error.message.includes('timeout')) {
      return {
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Network error. Please check your connection and try again.',
        details: error.message
      };
    }

    // Generic error
    return {
      success: false,
      error: 'UNKNOWN_ERROR',
      message: 'An unexpected error occurred. Please try again.',
      details: error.message
    };
  }

  /**
   * Process a user request through the agent workflow
   * @param {string} userId - User ID
   * @param {string} message - User message/request
   * @param {Object} context - Additional context
   * @returns {Promise<Object>} Agent response
   */
  async processRequest(userId, message, context = {}) {
    try {
      // Validate input parameters
      this._validateInput(userId, message, context);

      logger.info(`Processing spreadsheet agent request for user: ${userId}`);
      logger.debug(`Message: ${message}`);
      logger.debug(`Context:`, context);

      const initialState = {
        messages: [new HumanMessage(message)],
        userId,
        ...context
      };

      const result = await this.graph.invoke(initialState);

      logger.info(`Spreadsheet agent request completed for user: ${userId}`);
      logger.debug(`Result:`, result.result);

      return result;
    } catch (error) {
      logger.error('Error processing agent request:', error);
      return this._handleError(error);
    }
  }

  /**
   * Get agent status and health check
   * @returns {Object} Agent status
   */
  getStatus() {
    return {
      status: 'healthy',
      tools: this.tools.map(tool => ({
        name: tool.name,
        description: tool.description
      })),
      graphInitialized: !!this.graph,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Reset agent state (useful for testing)
   */
  reset() {
    logger.info('Resetting SpreadsheetsAgent');
    this._initializeGraph();
  }
}
